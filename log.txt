liangliangdamowang@yucool:~$ sudo systemctl status ssh
.
● ssh.service - OpenBSD Secure Shell server
     Loaded: loaded (/lib/systemd/system/ssh.service; enabled; preset: enabled)
     Active: active (running) since Tue 2025-07-29 21:02:07 CST; 12h ago
       Docs: man:sshd(8)
             man:sshd_config(5)
   Main PID: 19783 (sshd)
      Tasks: 7 (limit: 19175)
     Memory: 11.3M
        CPU: 1min 34.451s
     CGroup: /system.slice/ssh.service
             ├─ 19783 "sshd: /usr/sbin/sshd -D [listener] 4 of 10-100 startups"
             ├─204811 "sshd: [accepted]"
             ├─204860 "sshd: unknown [priv]"
             ├─204861 "sshd: unknown [net]"
             ├─204880 "sshd: [accepted]"
             ├─204898 "sshd: [accepted]"
             └─204900 "sshd: [net]"

Jul 30 09:29:19 yucool sshd[204782]: PAM 4 more authentication failures; logname= uid=0 euid=0 tty=ssh ruser= rhost=***********  user=root
Jul 30 09:29:19 yucool sshd[204782]: PAM service(sshd) ignoring max retries; 5 > 3
Jul 30 09:30:02 yucool sshd[204810]: Connection reset by ************** port 41204 [preauth]
Jul 30 09:30:29 yucool sshd[204860]: Invalid user test from *************** port 48601
Jul 30 09:30:29 yucool sshd[204860]: pam_unix(sshd:auth): check pass; user unknown
Jul 30 09:30:29 yucool sshd[204860]: pam_unix(sshd:auth): authentication failure; logname= uid=0 euid=0 tty=ssh ruser= rhost=***************
Jul 30 09:30:31 yucool sshd[204860]: Failed password for invalid user test from *************** port 48601 ssh2
Jul 30 09:31:08 yucool sshd[204901]: Accepted publickey for liangliangdamowang from ************* port 37245 ssh2: ECDSA SHA256:WY95T4SXD/1/zLKWy3gTvgPWVJRJTE0bdOD+feBQUZA
Jul 30 09:31:08 yucool sshd[204901]: pam_unix(sshd:session): session opened for user liangliangdamowang(uid=1000) by (uid=0)
Jul 30 09:31:08 yucool sshd[204901]: pam_env(sshd:session): deprecated reading of user environment enabled
-bash: .: filename argument required
.: usage: . filename [arguments]
liangliangdamowang@yucool:~$ sudo cat /etc/ssh/sshd_config | grep -E "(PermitRootLogin|PubkeyAuthentication|AuthorizedKeysFile|PasswordAuthentication)"
#PubkeyAuthentication yes
#AuthorizedKeysFile     .ssh/authorized_keys .ssh/authorized_keys2
PasswordAuthentication yes
# PasswordAuthentication.  Depending on your PAM configuration,
PermitRootLogin prohibit-password
# PAM authentication, then enable this but set PasswordAuthentication
liangliangdamowang@yucool:~$ sudo cat /root/.ssh/authorized_keys
# Added by Google
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCXwDznPboO+ejT7A7b/9Y8FWlmtBLKM01KVwDOSvwPhrR6YZC0ijGMmg6YjPcucBf0tp5kja5IbofeIOrh+mfvsaF2P8xAH9siq5fNR8sUgDg9MDbN/I2B96jAnYOBdjshxCz35Zx973Kq3A731KfAQTQjtoA70AXYrv1cmkjeevwwZkK/KNGwhjt37QEgZGJ7qIApkzo55HNZolLDGwyAOdbzJZaz4J80fTIyTH+Vi6PxCw5ADc/eLuewYhLhjFZjJUtSPyAoLk3Fj13lQW71Yk1pRAaR50q1mLPlIePPDh8SWqGvF7qtZ6f9TzHaWnMiYmkehTGtH2LKZeiQEwa+84tA5bcz4MiZMnBmqw+VjMKpyVOkIeavcixmwFHSmBX1OOfiBNha7/U9LiJQwkmkeM/1nmf0vBvuW1BdBgQTclp/+fuxI998Pd8tYQTmCNCZthwYp1FBvq3KMzM8uaSJtrHP3ONhSTMOlOCpPhbhQQ7nFV9M3a+cAMQVCALl4s0= vscode
liangliangdamowang@yucool:~$ ls -la /root/.ssh/
ls: cannot access '/root/.ssh/': Permission denied
liangliangdamowang@yucool:~$ sudo su
root@yucool:/home/<USER>/root/.ssh/
total 12
drwx------  2 <USER> <GROUP> 4096 Jul 23 22:30 .
dr-xr-x--- 18 <USER> <GROUP> 4096 Jul 30 09:31 ..
-rw-------  1 <USER> <GROUP>  578 Jul 24 11:22 authorized_keys
root@yucool:/home/<USER>/var/log/auth.log
2025-07-30T09:28:06.699688+08:00 yucool sshd[204732]: Invalid user wang from *************** port 51586
2025-07-30T09:28:06.701975+08:00 yucool sshd[204732]: pam_unix(sshd:auth): check pass; user unknown
2025-07-30T09:28:06.702072+08:00 yucool sshd[204732]: pam_unix(sshd:auth): authentication failure; logname= uid=0 euid=0 tty=ssh ruser= rhost=*************** 
2025-07-30T09:28:08.712407+08:00 yucool sshd[204732]: Failed password for invalid user wang from *************** port 51586 ssh2
2025-07-30T09:28:09.716247+08:00 yucool sshd[204732]: Received disconnect from *************** port 51586:11: Bye Bye [preauth]
2025-07-30T09:28:09.717804+08:00 yucool sshd[204732]: Disconnected from invalid user wang *************** port 51586 [preauth]
2025-07-30T09:28:16.054701+08:00 yucool sshd[204727]: error: kex_exchange_identification: read: Connection reset by peer
2025-07-30T09:28:16.054833+08:00 yucool sshd[204727]: Connection reset by ************** port 35306
2025-07-30T09:29:02.332142+08:00 yucool sshd[204780]: Invalid user user2 from ************ port 56562
2025-07-30T09:29:02.334681+08:00 yucool sshd[204780]: pam_unix(sshd:auth): check pass; user unknown
2025-07-30T09:29:02.334744+08:00 yucool sshd[204780]: pam_unix(sshd:auth): authentication failure; logname= uid=0 euid=0 tty=ssh ruser= rhost=************ 
2025-07-30T09:29:03.113072+08:00 yucool sshd[204782]: pam_unix(sshd:auth): authentication failure; logname= uid=0 euid=0 tty=ssh ruser= rhost=***********  user=root
2025-07-30T09:29:05.032721+08:00 yucool sshd[204780]: Failed password for invalid user user2 from ************ port 56562 ssh2
2025-07-30T09:29:05.615067+08:00 yucool sshd[204782]: Failed password for root from *********** port 57429 ssh2
2025-07-30T09:29:07.637872+08:00 yucool sshd[204780]: Received disconnect from ************ port 56562:11: Bye Bye [preauth]
2025-07-30T09:29:07.637996+08:00 yucool sshd[204780]: Disconnected from invalid user user2 ************ port 56562 [preauth]
2025-07-30T09:29:09.395676+08:00 yucool sshd[204782]: Failed password for root from *********** port 57429 ssh2
2025-07-30T09:29:12.097409+08:00 yucool sshd[204782]: Failed password for root from *********** port 57429 ssh2
2025-07-30T09:29:16.334890+08:00 yucool sshd[204782]: Failed password for root from *********** port 57429 ssh2
2025-07-30T09:29:19.538852+08:00 yucool sshd[204782]: Failed password for root from *********** port 57429 ssh2
2025-07-30T09:29:19.843278+08:00 yucool sshd[204782]: Received disconnect from *********** port 57429:11: Bye [preauth]
2025-07-30T09:29:19.843469+08:00 yucool sshd[204782]: Disconnected from authenticating user root *********** port 57429 [preauth]
2025-07-30T09:29:19.843623+08:00 yucool sshd[204782]: PAM 4 more authentication failures; logname= uid=0 euid=0 tty=ssh ruser= rhost=***********  user=root
2025-07-30T09:29:19.843684+08:00 yucool sshd[204782]: PAM service(sshd) ignoring max retries; 5 > 3
2025-07-30T09:30:02.222133+08:00 yucool sshd[204810]: Connection reset by ************** port 41204 [preauth]
2025-07-30T09:30:29.516833+08:00 yucool sshd[204860]: Invalid user test from *************** port 48601
2025-07-30T09:30:29.518808+08:00 yucool sshd[204860]: pam_unix(sshd:auth): check pass; user unknown
2025-07-30T09:30:29.518899+08:00 yucool sshd[204860]: pam_unix(sshd:auth): authentication failure; logname= uid=0 euid=0 tty=ssh ruser= rhost=*************** 
2025-07-30T09:30:31.493992+08:00 yucool sshd[204860]: Failed password for invalid user test from *************** port 48601 ssh2
2025-07-30T09:30:56.790114+08:00 yucool gpasswd[204885]: user liangliangdamowang added by root to group google-sudoers
2025-07-30T09:31:08.037115+08:00 yucool sshd[204901]: Accepted publickey for liangliangdamowang from ************* port 37245 ssh2: ECDSA SHA256:WY95T4SXD/1/zLKWy3gTvgPWVJRJTE0bdOD+feBQUZA
2025-07-30T09:31:08.038571+08:00 yucool sshd[204901]: pam_unix(sshd:session): session opened for user liangliangdamowang(uid=1000) by (uid=0)
2025-07-30T09:31:08.046020+08:00 yucool systemd-logind[19314]: New session 47 of user liangliangdamowang.
2025-07-30T09:31:08.079082+08:00 yucool (systemd): pam_unix(systemd-user:session): session opened for user liangliangdamowang(uid=1000) by (uid=0)
2025-07-30T09:31:08.181903+08:00 yucool sshd[204901]: pam_env(sshd:session): deprecated reading of user environment enabled
2025-07-30T09:31:19.650834+08:00 yucool sudo: liangliangdamowang : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/systemctl status ssh
2025-07-30T09:31:19.651482+08:00 yucool sudo: pam_unix(sudo:session): session opened for user root(uid=0) by liangliangdamowang(uid=1000)
2025-07-30T09:31:19.662902+08:00 yucool sudo: pam_unix(sudo:session): session closed for user root
2025-07-30T09:31:28.815932+08:00 yucool sudo: liangliangdamowang : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/cat /etc/ssh/sshd_config
2025-07-30T09:31:28.816279+08:00 yucool sudo: pam_unix(sudo:session): session opened for user root(uid=0) by liangliangdamowang(uid=1000)
2025-07-30T09:31:28.818423+08:00 yucool sudo: pam_unix(sudo:session): session closed for user root
2025-07-30T09:31:34.330826+08:00 yucool sudo: liangliangdamowang : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/cat /root/.ssh/authorized_keys
2025-07-30T09:31:34.331268+08:00 yucool sudo: pam_unix(sudo:session): session opened for user root(uid=0) by liangliangdamowang(uid=1000)
2025-07-30T09:31:34.333164+08:00 yucool sudo: pam_unix(sudo:session): session closed for user root
2025-07-30T09:31:47.300323+08:00 yucool sudo: liangliangdamowang : TTY=pts/0 ; PWD=/home/<USER>/usr/bin/su
2025-07-30T09:31:47.301054+08:00 yucool sudo: pam_unix(sudo:session): session opened for user root(uid=0) by liangliangdamowang(uid=1000)
2025-07-30T09:31:47.304122+08:00 yucool su[204970]: (to root) root on pts/1
2025-07-30T09:31:47.304399+08:00 yucool su[204970]: pam_unix(su:session): session opened for user root(uid=0) by liangliangdamowang(uid=0)
2025-07-30T09:31:57.795639+08:00 yucool sudo:     root : TTY=pts/1 ; PWD=/home/<USER>/usr/bin/tail -n 50 /var/log/auth.log
2025-07-30T09:31:57.796011+08:00 yucool sudo: pam_unix(sudo:session): session opened for user root(uid=0) by liangliangdamowang(uid=0)
root@yucool:/home/<USER>
Status: active

To                         Action      From
--                         ------      ----
22/tcp                     ALLOW       Anywhere                  
80/tcp                     ALLOW       Anywhere                  
443/tcp                    ALLOW       Anywhere                  
3000/tcp                   ALLOW       Anywhere                  
1314/tcp                   ALLOW       Anywhere                  
20/tcp                     ALLOW       Anywhere                  
21/tcp                     ALLOW       Anywhere                  
888/tcp                    ALLOW       Anywhere                  
37698/tcp                  ALLOW       Anywhere                  
39000:40000/tcp            ALLOW       Anywhere                  
8888                       ALLOW       Anywhere                  
8888/tcp                   ALLOW       Anywhere                  
13818/tcp                  ALLOW       Anywhere                  
22/tcp (v6)                ALLOW       Anywhere (v6)             
80/tcp (v6)                ALLOW       Anywhere (v6)             
443/tcp (v6)               ALLOW       Anywhere (v6)             
3000/tcp (v6)              ALLOW       Anywhere (v6)             
1314/tcp (v6)              ALLOW       Anywhere (v6)             
8888 (v6)                  ALLOW       Anywhere (v6)             
20/tcp (v6)                ALLOW       Anywhere (v6)             
21/tcp (v6)                ALLOW       Anywhere (v6)             
888/tcp (v6)               ALLOW       Anywhere (v6)             
8888/tcp (v6)              ALLOW       Anywhere (v6)             
39000:40000/tcp (v6)       ALLOW       Anywhere (v6)             
13818/tcp (v6)             ALLOW       Anywhere (v6) 