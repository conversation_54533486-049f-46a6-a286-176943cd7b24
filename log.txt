liangliangdamowang@yucool:~$ sudo netstat -tn | grep :22 | wc -l

1
liangliangdamowang@yucool:~$ sudo ps aux | grep sshd
root       19783  0.0  0.0  15440  9572 ?        Ss   Jul29   0:01 sshd: /usr/sbin/sshd -D [listener] 0 of 10-100 startups
root      213125  0.0  0.0  17688 10912 ?        Ss   10:09   0:00 sshd: liangliangdamowang [priv]
liangli+  213149  0.0  0.0  17848  6596 ?        S    10:09   0:00 sshd: liangliangdamowang@pts/0
liangli+  213205  0.0  0.0   3748  1904 pts/0    S+   10:10   0:00 grep sshd
liangliangdamowang@yucool:~$ uptime
 10:10:31 up 14:58,  2 users,  load average: 0.00, 0.02, 0.12
liangliangdamowang@yucool:~$ free -h
               total        used        free      shared  buff/cache   available
Mem:            15Gi       3.0Gi       8.5Gi       1.8Mi       4.4Gi        12Gi
Swap:             0B          0B          0B
liangliangdamowang@yucool:~$ sudo sshd -T | grep -E "(maxstartups|maxauthtries|maxsessions)"
maxauthtries 6
maxsessions 10
maxstartups 10:30:100
persourcemaxstartups none
liangliangdamowang@yucool:~$ sudo cat /proc/sys/net/netfilter/nf_conntrack_count
38
liangliangdamowang@yucool:~$ sudo cat /proc/sys/net/netfilter/nf_conntrack_max
262144
liangliangdamowang@yucool:~$ sudo systemctl restart ssh